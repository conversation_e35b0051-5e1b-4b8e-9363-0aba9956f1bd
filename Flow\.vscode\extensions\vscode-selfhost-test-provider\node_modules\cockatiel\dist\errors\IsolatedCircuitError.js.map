{"version": 3, "file": "IsolatedCircuitError.js", "sourceRoot": "", "sources": ["../../src/errors/IsolatedCircuitError.ts"], "names": [], "mappings": ";;;AAAA,6DAA0D;AAE1D,MAAa,oBAAqB,SAAQ,uCAAkB;IAG1D;;;OAGG;IACH;QACE,KAAK,CAAC,yDAAyD,CAAC,CAAC;QAPnD,2BAAsB,GAAG,IAAI,CAAC;IAQ9C,CAAC;CACF;AAVD,oDAUC", "sourcesContent": ["import { BrokenCircuitError } from './BrokenCircuitError';\n\nexport class IsolatedCircuitError extends BrokenCircuitError {\n  public readonly isIsolatedCircuitError = true;\n\n  /**\n   * Exception thrown from {@link CircuitBreakerPolicy.execute} when the\n   * circuit breaker is open.\n   */\n  constructor() {\n    super(`Execution prevented because the circuit breaker is open`);\n  }\n}\n"]}