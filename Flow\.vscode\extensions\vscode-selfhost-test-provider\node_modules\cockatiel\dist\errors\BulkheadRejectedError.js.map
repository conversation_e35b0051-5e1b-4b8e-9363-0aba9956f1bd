{"version": 3, "file": "BulkheadRejectedError.js", "sourceRoot": "", "sources": ["../../src/errors/BulkheadRejectedError.ts"], "names": [], "mappings": ";;;AAAA,MAAa,qBAAsB,SAAQ,KAAK;IAG9C,YAAY,cAAsB,EAAE,UAAkB;QACpD,KAAK,CACH,iCAAiC,cAAc,uBAAuB,UAAU,aAAa,CAC9F,CAAC;QALY,4BAAuB,GAAG,IAAI,CAAC;IAM/C,CAAC;CACF;AARD,sDAQC", "sourcesContent": ["export class BulkheadRejectedError extends Error {\n  public readonly isBulkheadRejectedError = true;\n\n  constructor(executionSlots: number, queueSlots: number) {\n    super(\n      `Bulkhead capacity exceeded (0/${executionSlots} execution slots, 0/${queueSlots} available)`,\n    );\n  }\n}\n"]}