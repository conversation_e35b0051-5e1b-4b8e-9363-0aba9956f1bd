{"version": 3, "file": "IterableBackoff.js", "sourceRoot": "", "sources": ["../../../src/backoff/IterableBackoff.ts"], "names": [], "mappings": "AAEA,MAAM,OAAO,eAAe;IAC1B;;OAEG;IACH,YAA6B,SAAgC;QAAhC,cAAS,GAAT,SAAS,CAAuB;IAAG,CAAC;IAEjE;;OAEG;IACI,IAAI;QACT,OAAO,QAAQ,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;IACrC,CAAC;CACF;AAED,MAAM,QAAQ,GAAG,CAAC,SAAgC,EAAE,KAAa,EAAqB,EAAE,CAAC,CAAC;IACxF,QAAQ,EAAE,SAAS,CAAC,KAAK,CAAC;IAC1B,IAAI;QACF,OAAO,KAAK,KAAK,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;IAChF,CAAC;CACF,CAAC,CAAC", "sourcesContent": ["import { IBackoff, IBackoffFactory } from './Backoff';\n\nexport class IterableBackoff implements IBackoffFactory<unknown> {\n  /**\n   * Backoff that returns a number from an iterable.\n   */\n  constructor(private readonly durations: ReadonlyArray<number>) {}\n\n  /**\n   * @inheritdoc\n   */\n  public next() {\n    return instance(this.durations, 0);\n  }\n}\n\nconst instance = (durations: ReadonlyArray<number>, index: number): IBackoff<unknown> => ({\n  duration: durations[index],\n  next() {\n    return index === durations.length - 1 ? this : instance(durations, index + 1);\n  },\n});\n"]}