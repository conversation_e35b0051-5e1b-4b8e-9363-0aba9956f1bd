{"version": 3, "file": "Executor.js", "sourceRoot": "", "sources": ["../../../src/common/Executor.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,MAAM,SAAS,CAAC;AAIvC,MAAM,CAAC,MAAM,aAAa,GAAG,CAAI,OAA4B,EAAE,EAAE;IAC/D,IAAI,OAAO,IAAI,OAAO,EAAE;QACtB,MAAM,OAAO,CAAC,KAAK,CAAC;KACrB;IAED,IAAI,SAAS,IAAI,OAAO,EAAE;QACxB,OAAO,OAAO,CAAC,OAAO,CAAC;KACxB;IAED,OAAO,OAAO,CAAC,KAAK,CAAC;AACvB,CAAC,CAAC;AAIF,MAAM,aAAa,GAAG,GAAG,EAAE;IACzB,IAAI,OAAO,WAAW,KAAK,WAAW,EAAE;QACtC,MAAM,KAAK,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;QAChC,OAAO,GAAG,EAAE,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC;KACxC;SAAM;QACL,MAAM,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;QACtC,OAAO,GAAG,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,KAAK,CAAC,GAAG,OAAO,CAAC,CAAC,SAAS;KAC1E;AACH,CAAC,CAAC;AAEF,MAAM,OAAO,cAAc;IAMzB,YACmB,cAAyC,GAAG,EAAE,CAAC,KAAK,EACpD,eAA6C,GAAG,EAAE,CAAC,KAAK;QADxD,gBAAW,GAAX,WAAW,CAAyC;QACpD,iBAAY,GAAZ,YAAY,CAA4C;QAP1D,mBAAc,GAAG,IAAI,YAAY,EAAiB,CAAC;QACnD,mBAAc,GAAG,IAAI,YAAY,EAAiB,CAAC;QACpD,cAAS,GAAG,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC;QAC5C,cAAS,GAAG,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC;IAKzD,CAAC;IAEG,KAAK;QACV,OAAO,IAAI,cAAc,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;IACjE,CAAC;IAEM,KAAK,CAAC,MAAM,CACjB,EAAsC,EACtC,GAAG,IAAO;QAEV,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;QAEhG,IAAI;YACF,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;YAChC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE;gBAC7B,IAAI,SAAS,EAAE;oBACb,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,EAAE,CAAC,CAAC;iBACrD;gBACD,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;aAC3B;YAED,IAAI,SAAS,EAAE;gBACb,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;aACvF;YAED,OAAO,EAAE,KAAK,EAAE,CAAC;SAClB;QAAC,OAAO,QAAQ,EAAE;YACjB,MAAM,KAAK,GAAG,QAAiB,CAAC;YAChC,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,KAAc,CAAC,CAAC;YACjD,IAAI,SAAS,EAAE;gBACb,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;aACjF;YAED,IAAI,CAAC,OAAO,EAAE;gBACZ,MAAM,KAAK,CAAC;aACb;YAED,OAAO,EAAE,KAAK,EAAE,CAAC;SAClB;IACH,CAAC;CACF", "sourcesContent": ["import { FailureReason, IFailureEvent, ISuccessEvent } from '../Policy';\nimport { EventEmitter } from './Event';\n\nexport type FailureOrSuccess<R> = FailureReason<R> | { success: R };\n\nexport const returnOrThrow = <R>(failure: FailureOrSuccess<R>) => {\n  if ('error' in failure) {\n    throw failure.error;\n  }\n\n  if ('success' in failure) {\n    return failure.success;\n  }\n\n  return failure.value;\n};\n\ndeclare const performance: { now(): number };\n\nconst makeStopwatch = () => {\n  if (typeof performance !== 'undefined') {\n    const start = performance.now();\n    return () => performance.now() - start;\n  } else {\n    const start = process.hrtime.bigint();\n    return () => Number(process.hrtime.bigint() - start) / 1000000; // ns->ms\n  }\n};\n\nexport class ExecuteWrapper {\n  private readonly successEmitter = new EventEmitter<ISuccessEvent>();\n  private readonly failureEmitter = new EventEmitter<IFailureEvent>();\n  public readonly onSuccess = this.successEmitter.addListener;\n  public readonly onFailure = this.failureEmitter.addListener;\n\n  constructor(\n    private readonly errorFilter: (error: Error) => boolean = () => false,\n    private readonly resultFilter: (result: unknown) => boolean = () => false,\n  ) {}\n\n  public clone() {\n    return new ExecuteWrapper(this.errorFilter, this.resultFilter);\n  }\n\n  public async invoke<T extends unknown[], R>(\n    fn: (...args: T) => PromiseLike<R> | R,\n    ...args: T\n  ): Promise<FailureOrSuccess<R>> {\n    const stopwatch = this.successEmitter.size || this.failureEmitter.size ? makeStopwatch() : null;\n\n    try {\n      const value = await fn(...args);\n      if (!this.resultFilter(value)) {\n        if (stopwatch) {\n          this.successEmitter.emit({ duration: stopwatch() });\n        }\n        return { success: value };\n      }\n\n      if (stopwatch) {\n        this.failureEmitter.emit({ duration: stopwatch(), handled: true, reason: { value } });\n      }\n\n      return { value };\n    } catch (rawError) {\n      const error = rawError as Error;\n      const handled = this.errorFilter(error as Error);\n      if (stopwatch) {\n        this.failureEmitter.emit({ duration: stopwatch(), handled, reason: { error } });\n      }\n\n      if (!handled) {\n        throw error;\n      }\n\n      return { error };\n    }\n  }\n}\n"]}