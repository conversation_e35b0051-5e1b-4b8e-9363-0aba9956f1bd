{"version": 3, "file": "ConstantBackoff.js", "sourceRoot": "", "sources": ["../../src/backoff/ConstantBackoff.ts"], "names": [], "mappings": ";;;AAEA,MAAa,eAAe;IAC1B;;OAEG;IACH,YAA6B,QAAgB;QAAhB,aAAQ,GAAR,QAAQ,CAAQ;IAAG,CAAC;IAEjD;;OAEG;IACI,IAAI;QACT,OAAO,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACjC,CAAC;CACF;AAZD,0CAYC;AAED,MAAM,QAAQ,GAAG,CAAC,QAAgB,EAAqB,EAAE,CAAC,CAAC;IACzD,QAAQ,EAAE,QAAQ;IAClB,IAAI;QACF,OAAO,IAAI,CAAC;IACd,CAAC;CACF,CAAC,CAAC", "sourcesContent": ["import { IBackoff, IBackoffFactory } from './Backoff';\n\nexport class Con<PERSON><PERSON>ackoff implements IBackoffFactory<unknown> {\n  /**\n   * Backoff that returns a constant interval.\n   */\n  constructor(private readonly interval: number) {}\n\n  /**\n   * @inheritdoc\n   */\n  public next() {\n    return instance(this.interval);\n  }\n}\n\nconst instance = (interval: number): IBackoff<unknown> => ({\n  duration: interval,\n  next() {\n    return this;\n  },\n});\n"]}