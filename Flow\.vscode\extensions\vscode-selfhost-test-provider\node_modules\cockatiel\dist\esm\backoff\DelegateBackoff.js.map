{"version": 3, "file": "DelegateBackoff.js", "sourceRoot": "", "sources": ["../../../src/backoff/DelegateBackoff.ts"], "names": [], "mappings": "AAOA,MAAM,OAAO,eAAe;IAC1B;;;;OAIG;IACH,YAA6B,EAA2B;QAA3B,OAAE,GAAF,EAAE,CAAyB;IAAG,CAAC;IAE5D;;OAEG;IACI,IAAI,CAAC,OAAU;QACpB,OAAO,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACzC,CAAC;CACF;AAED,MAAM,QAAQ,GAAG,CAAO,EAA2B,EAAE,KAAS,EAAE,OAAO,GAAG,CAAC,EAAe,EAAE,CAAC,CAAC;IAC5F,QAAQ,EAAE,OAAO;IACjB,IAAI,CAAC,OAAU;QACb,MAAM,MAAM,GAAG,EAAE,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAClC,OAAO,OAAO,MAAM,KAAK,QAAQ;YAC/B,CAAC,CAAC,QAAQ,CAAC,EAAE,EAAE,KAAK,EAAE,MAAM,CAAC;YAC7B,CAAC,CAAC,QAAQ,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;IAC/C,CAAC;CACF,CAAC,CAAC", "sourcesContent": ["import { IBackoff, IBackoffFactory } from './Backoff';\n\nexport type DelegateBackoffFn<T, S = void> = (\n  context: T,\n  state?: S,\n) => { delay: number; state: S } | number;\n\nexport class DelegateBackoff<T, S = void> implements IBackoffFactory<T> {\n  /**\n   * Backoff that delegates to a user-provided function. The function takes\n   * the backoff context, and can optionally take (and return) a state value\n   * that will be passed into subsequent backoff requests.\n   */\n  constructor(private readonly fn: DelegateBackoffFn<T, S>) {}\n\n  /**\n   * @inheritdoc\n   */\n  public next(context: T) {\n    return instance(this.fn).next(context);\n  }\n}\n\nconst instance = <T, S>(fn: DelegateBackoffFn<T, S>, state?: S, current = 0): IBackoff<T> => ({\n  duration: current,\n  next(context: T) {\n    const result = fn(context, state);\n    return typeof result === 'number'\n      ? instance(fn, state, result)\n      : instance(fn, result.state, result.delay);\n  },\n});\n"]}