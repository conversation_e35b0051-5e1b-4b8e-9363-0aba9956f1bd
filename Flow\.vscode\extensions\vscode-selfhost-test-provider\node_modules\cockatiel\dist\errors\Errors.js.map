{"version": 3, "file": "Errors.js", "sourceRoot": "", "sources": ["../../src/errors/Errors.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAKA,uDAAqC;AACrC,0DAAwC;AACxC,yDAAuC;AACvC,uDAAqC;AAE9B,MAAM,oBAAoB,GAAG,CAAC,CAAU,EAA2B,EAAE,CAC1E,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,KAAK,IAAI,sBAAsB,IAAI,CAAC,CAAC;AAD9C,QAAA,oBAAoB,wBAC0B;AAEpD,MAAM,uBAAuB,GAAG,CAAC,CAAU,EAA8B,EAAE,CAChF,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,KAAK,IAAI,yBAAyB,IAAI,CAAC,CAAC;AADjD,QAAA,uBAAuB,2BAC0B;AAEvD,MAAM,sBAAsB,GAAG,CAAC,CAAU,EAA6B,EAAE,CAC9E,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,KAAK,IAAI,yBAAyB,IAAI,CAAC,CAAC;AADjD,QAAA,sBAAsB,0BAC2B;AAEvD,MAAM,oBAAoB,GAAG,CAAC,CAAU,EAA2B,EAAE,CAC1E,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,KAAK,IAAI,yBAAyB,IAAI,CAAC,CAAC;AADjD,QAAA,oBAAoB,wBAC6B", "sourcesContent": ["import { BrokenCircuitError } from './BrokenCircuitError';\nimport { BulkheadRejectedError } from './BulkheadRejectedError';\nimport { IsolatedCircuitError } from './IsolatedCircuitError';\nimport { TaskCancelledError } from './TaskCancelledError';\n\nexport * from './BrokenCircuitError';\nexport * from './BulkheadRejectedError';\nexport * from './IsolatedCircuitError';\nexport * from './TaskCancelledError';\n\nexport const isBrokenCircuitError = (e: unknown): e is BrokenCircuitError =>\n  !!e && e instanceof Error && 'isBrokenCircuitError' in e;\n\nexport const isBulkheadRejectedError = (e: unknown): e is BulkheadRejectedError =>\n  !!e && e instanceof Error && 'isBulkheadRejectedError' in e;\n\nexport const isIsolatedCircuitError = (e: unknown): e is IsolatedCircuitError =>\n  !!e && e instanceof Error && 'isBulkheadRejectedError' in e;\n\nexport const isTaskCancelledError = (e: unknown): e is TaskCancelledError =>\n  !!e && e instanceof Error && 'isBulkheadRejectedError' in e;\n"]}