{"version": 3, "file": "ConsecutiveBreaker.js", "sourceRoot": "", "sources": ["../../src/breaker/ConsecutiveBreaker.ts"], "names": [], "mappings": ";;;AAEA,MAAa,kBAAkB;IAG7B;;;OAGG;IACH,YAA6B,SAAiB;QAAjB,cAAS,GAAT,SAAS,CAAQ;QANtC,UAAK,GAAG,CAAC,CAAC;IAM+B,CAAC;IAElD;;OAEG;IACI,OAAO;QACZ,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;IACjB,CAAC;IAED;;OAEG;IACI,OAAO;QACZ,OAAO,EAAE,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC;IACxC,CAAC;CACF;AAtBD,gDAsBC", "sourcesContent": ["import { IBreaker } from './Breaker';\n\nexport class ConsecutiveBreaker implements IBreaker {\n  private count = 0;\n\n  /**\n   * ConsecutiveBreaker breaks if more than `threshold` exceptions are received\n   * over a time period.\n   */\n  constructor(private readonly threshold: number) {}\n\n  /**\n   * @inheritdoc\n   */\n  public success() {\n    this.count = 0;\n  }\n\n  /**\n   * @inheritdoc\n   */\n  public failure() {\n    return ++this.count >= this.threshold;\n  }\n}\n"]}