{"version": 3, "file": "abort.js", "sourceRoot": "", "sources": ["../../src/common/abort.ts"], "names": [], "mappings": ";;;AAAA,mCAAkC;AAErB,QAAA,kBAAkB,GAAG,IAAI,eAAe,EAAE,CAAC,MAAM,CAAC;AAE/D,MAAM,YAAY,GAAG,IAAI,eAAe,EAAE,CAAC;AAC3C,YAAY,CAAC,KAAK,EAAE,CAAC;AACR,QAAA,aAAa,GAAG,YAAY,CAAC,MAAM,CAAC;AAEjD;;;GAGG;AACI,MAAM,qBAAqB,GAAG,CAAC,MAAoB,EAAE,EAAE;IAC5D,MAAM,IAAI,GAAG,IAAI,eAAe,EAAE,CAAC;IACnC,IAAI,CAAC,MAAM,EAAE;QACX,OAAO,IAAI,CAAC;KACb;IAED,IAAI,MAAM,CAAC,OAAO,EAAE;QAClB,IAAI,CAAC,KAAK,EAAE,CAAC;KACd;IAED,IAAI,MAAM,KAAK,0BAAkB,EAAE;QACjC,MAAM,GAAG,GAAG,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;QAC9B,IAAA,eAAO,EAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;KAC7C;IAED,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAhBW,QAAA,qBAAqB,yBAgBhC", "sourcesContent": ["import { onAbort } from './Event';\n\nexport const neverAbortedSignal = new AbortController().signal;\n\nconst cancelledSrc = new AbortController();\ncancelledSrc.abort();\nexport const abortedSignal = cancelledSrc.signal;\n\n/**\n * Creates a new AbortController that is aborted when the parent signal aborts.\n * @private\n */\nexport const deriveAbortController = (signal?: AbortSignal) => {\n  const ctrl = new AbortController();\n  if (!signal) {\n    return ctrl;\n  }\n\n  if (signal.aborted) {\n    ctrl.abort();\n  }\n\n  if (signal !== neverAbortedSignal) {\n    const ref = new WeakRef(ctrl);\n    onAbort(signal)(() => ref.deref()?.abort());\n  }\n\n  return ctrl;\n};\n"]}