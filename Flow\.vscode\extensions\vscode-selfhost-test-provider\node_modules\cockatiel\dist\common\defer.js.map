{"version": 3, "file": "defer.js", "sourceRoot": "", "sources": ["../../src/common/defer.ts"], "names": [], "mappings": ";;;AAAO,MAAM,KAAK,GAAG,GAAM,EAAE;IAC3B,IAAI,OAA2B,CAAC;IAChC,IAAI,MAA8B,CAAC;IACnC,MAAM,OAAO,GAAG,IAAI,OAAO,CAAI,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;QAC1C,OAAO,GAAG,GAAG,CAAC;QACd,MAAM,GAAG,GAAG,CAAC;IACf,CAAC,CAAC,CAAC;IAEH,oEAAoE;IACpE,OAAO,EAAE,OAAO,EAAE,OAAQ,EAAE,MAAM,EAAE,MAAO,EAAE,OAAO,EAAE,CAAC;AACzD,CAAC,CAAC;AAVW,QAAA,KAAK,SAUhB", "sourcesContent": ["export const defer = <T>() => {\n  let resolve: (value: T) => void;\n  let reject: (error: Error) => void;\n  const promise = new Promise<T>((res, rej) => {\n    resolve = res;\n    reject = rej;\n  });\n\n  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n  return { resolve: resolve!, reject: reject!, promise };\n};\n"]}