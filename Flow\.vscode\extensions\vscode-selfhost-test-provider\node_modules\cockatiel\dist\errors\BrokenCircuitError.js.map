{"version": 3, "file": "BrokenCircuitError.js", "sourceRoot": "", "sources": ["../../src/errors/BrokenCircuitError.ts"], "names": [], "mappings": ";;;AAAA,MAAa,kBAAmB,SAAQ,KAAK;IAE3C;;;OAGG;IACH,YAAY,OAAO,GAAG,yDAAyD;QAC7E,KAAK,CAAC,OAAO,CAAC,CAAC;QAND,yBAAoB,GAAG,IAAI,CAAC;IAO5C,CAAC;CACF;AATD,gDASC", "sourcesContent": ["export class BrokenCircuitError extends Error {\n  public readonly isBrokenCircuitError = true;\n  /**\n   * Exception thrown from {@link CircuitBreakerPolicy.execute} when the\n   * circuit breaker is open.\n   */\n  constructor(message = 'Execution prevented because the circuit breaker is open') {\n    super(message);\n  }\n}\n"]}