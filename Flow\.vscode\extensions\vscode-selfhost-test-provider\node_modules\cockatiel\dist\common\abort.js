"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deriveAbortController = exports.abortedSignal = exports.neverAbortedSignal = void 0;
const Event_1 = require("./Event");
exports.neverAbortedSignal = new AbortController().signal;
const cancelledSrc = new AbortController();
cancelledSrc.abort();
exports.abortedSignal = cancelledSrc.signal;
/**
 * Creates a new AbortController that is aborted when the parent signal aborts.
 * @private
 */
const deriveAbortController = (signal) => {
    const ctrl = new AbortController();
    if (!signal) {
        return ctrl;
    }
    if (signal.aborted) {
        ctrl.abort();
    }
    if (signal !== exports.neverAbortedSignal) {
        const ref = new WeakRef(ctrl);
        (0, Event_1.onAbort)(signal)(() => ref.deref()?.abort());
    }
    return ctrl;
};
exports.deriveAbortController = deriveAbortController;
//# sourceMappingURL=abort.js.map