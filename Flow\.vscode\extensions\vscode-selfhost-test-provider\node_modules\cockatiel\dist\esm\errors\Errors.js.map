{"version": 3, "file": "Errors.js", "sourceRoot": "", "sources": ["../../../src/errors/Errors.ts"], "names": [], "mappings": "AAKA,cAAc,sBAAsB,CAAC;AACrC,cAAc,yBAAyB,CAAC;AACxC,cAAc,wBAAwB,CAAC;AACvC,cAAc,sBAAsB,CAAC;AAErC,MAAM,CAAC,MAAM,oBAAoB,GAAG,CAAC,CAAU,EAA2B,EAAE,CAC1E,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,KAAK,IAAI,sBAAsB,IAAI,CAAC,CAAC;AAE3D,MAAM,CAAC,MAAM,uBAAuB,GAAG,CAAC,CAAU,EAA8B,EAAE,CAChF,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,KAAK,IAAI,yBAAyB,IAAI,CAAC,CAAC;AAE9D,MAAM,CAAC,MAAM,sBAAsB,GAAG,CAAC,CAAU,EAA6B,EAAE,CAC9E,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,KAAK,IAAI,yBAAyB,IAAI,CAAC,CAAC;AAE9D,MAAM,CAAC,MAAM,oBAAoB,GAAG,CAAC,CAAU,EAA2B,EAAE,CAC1E,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,KAAK,IAAI,yBAAyB,IAAI,CAAC,CAAC", "sourcesContent": ["import { BrokenCircuitError } from './BrokenCircuitError';\nimport { BulkheadRejectedError } from './BulkheadRejectedError';\nimport { IsolatedCircuitError } from './IsolatedCircuitError';\nimport { TaskCancelledError } from './TaskCancelledError';\n\nexport * from './BrokenCircuitError';\nexport * from './BulkheadRejectedError';\nexport * from './IsolatedCircuitError';\nexport * from './TaskCancelledError';\n\nexport const isBrokenCircuitError = (e: unknown): e is BrokenCircuitError =>\n  !!e && e instanceof Error && 'isBrokenCircuitError' in e;\n\nexport const isBulkheadRejectedError = (e: unknown): e is BulkheadRejectedError =>\n  !!e && e instanceof Error && 'isBulkheadRejectedError' in e;\n\nexport const isIsolatedCircuitError = (e: unknown): e is IsolatedCircuitError =>\n  !!e && e instanceof Error && 'isBulkheadRejectedError' in e;\n\nexport const isTaskCancelledError = (e: unknown): e is TaskCancelledError =>\n  !!e && e instanceof Error && 'isBulkheadRejectedError' in e;\n"]}