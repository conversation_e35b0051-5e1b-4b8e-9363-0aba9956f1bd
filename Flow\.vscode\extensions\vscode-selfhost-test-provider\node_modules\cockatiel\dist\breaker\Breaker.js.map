{"version": 3, "file": "Breaker.js", "sourceRoot": "", "sources": ["../../src/breaker/Breaker.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAiBA,oDAAkC;AAClC,uDAAqC", "sourcesContent": ["import { CircuitState } from '../CircuitBreakerPolicy';\n\n/**\n * The breaker determines when the circuit breaker should open.\n */\nexport interface IBreaker {\n  /**\n   * Called when a call succeeds.\n   */\n  success(state: CircuitState): void;\n\n  /**\n   * Called when a call fails. Returns true if the circuit should open.\n   */\n  failure(state: CircuitState): boolean;\n}\n\nexport * from './SamplingBreaker';\nexport * from './ConsecutiveBreaker';\n"]}