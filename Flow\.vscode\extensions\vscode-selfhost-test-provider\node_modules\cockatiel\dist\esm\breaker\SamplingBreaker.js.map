{"version": 3, "file": "SamplingBreaker.js", "sourceRoot": "", "sources": ["../../../src/breaker/SamplingBreaker.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,yBAAyB,CAAC;AA4BvD,MAAM,OAAO,eAAe;IAW1B;;;;OAIG;IACH,YAAY,EAAE,SAAS,EAAE,QAAQ,EAAE,gBAAgB,EAAE,UAAU,EAA2B;QAVlF,YAAO,GAAc,EAAE,CAAC;QACxB,kBAAa,GAAG,CAAC,CAAC;QAClB,oBAAe,GAAG,CAAC,CAAC;QACpB,qBAAgB,GAAG,CAAC,CAAC;QAQ3B,IAAI,SAAS,IAAI,CAAC,IAAI,SAAS,IAAI,CAAC,EAAE;YACpC,MAAM,IAAI,UAAU,CAAC,2DAA2D,SAAS,EAAE,CAAC,CAAC;SAC9F;QAED,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAE3B,yCAAyC;QACzC,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,CAAC,CAAC;QACpE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,EAAE,EAAE;YACpC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;SAChE;QAED,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,GAAG,WAAW,CAAC,CAAC;QAC7D,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU,GAAG,WAAW,CAAC;QAE9C,IAAI,UAAU,EAAE;YACd,IAAI,CAAC,WAAW,GAAG,UAAU,GAAG,IAAI,CAAC;SACtC;aAAM;YACL,8DAA8D;YAC9D,iCAAiC;YACjC,IAAI,CAAC,WAAW,GAAG,CAAC,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC;SAC3C;IACH,CAAC;IAED;;OAEG;IACI,OAAO,CAAC,KAAmB;QAChC,IAAI,KAAK,KAAK,YAAY,CAAC,QAAQ,EAAE;YACnC,IAAI,CAAC,YAAY,EAAE,CAAC;SACrB;QAED,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAClB,CAAC;IAED;;OAEG;IACI,OAAO,CAAC,KAAmB;QAChC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAEjB,IAAI,KAAK,KAAK,YAAY,CAAC,MAAM,EAAE;YACjC,OAAO,IAAI,CAAC;SACb;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,eAAe,CAAC;QAE3D,yDAAyD;QACzD,yCAAyC;QACzC,kCAAkC;QAClC,2DAA2D;QAC3D,IAAI,KAAK,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE;YAC5C,OAAO,KAAK,CAAC;SACd;QAED,6CAA6C;QAC7C,oCAAoC;QACpC,oCAAoC;QACpC,IAAI,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,SAAS,GAAG,KAAK,EAAE;YACjD,OAAO,IAAI,CAAC;SACb;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,YAAY;QAClB,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;QACzB,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;QAC1B,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE;YACjC,MAAM,CAAC,QAAQ,GAAG,CAAC,CAAC;YACpB,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC;YACrB,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC;SACtB;IACH,CAAC;IAEO,YAAY,CAAC,GAAW;QAC9B,MAAM,IAAI,GAAG,CAAC,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;QAC5D,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC;QACpD,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC;QACtD,MAAM,MAAM,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,QAAQ,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,EAAE,CAAC,CAAC;QACpF,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAE1B,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,IAAI,CAAC,OAAgB;QAC3B,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAEvB,2DAA2D;QAC3D,IAAI,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC9C,IAAI,GAAG,GAAG,MAAM,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU,EAAE;YAC7C,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;SACjC;QAED,2BAA2B;QAC3B,IAAI,OAAO,EAAE;YACX,MAAM,CAAC,SAAS,EAAE,CAAC;YACnB,IAAI,CAAC,gBAAgB,EAAE,CAAC;SACzB;aAAM;YACL,MAAM,CAAC,QAAQ,EAAE,CAAC;YAClB,IAAI,CAAC,eAAe,EAAE,CAAC;SACxB;IACH,CAAC;CACF", "sourcesContent": ["import { CircuitState } from '../CircuitBreakerPolicy';\nimport { IBreaker } from './Breaker';\n\ninterface IWindow {\n  startedAt: number;\n  failures: number;\n  successes: number;\n}\n\nexport interface ISamplingBreakerOptions {\n  /**\n   * Percentage (from 0 to 1) of requests that need to fail before we'll\n   * open the circuit.\n   */\n  threshold: number;\n\n  /**\n   * Length of time over which to sample.\n   */\n  duration: number;\n\n  /**\n   * Minimum number of RPS needed to be able to (potentially) open the circuit.\n   * Useful to avoid unnecessarily tripping under low load.\n   */\n  minimumRps?: number;\n}\n\nexport class SamplingBreaker implements IBreaker {\n  private readonly threshold: number;\n  private readonly minimumRpms: number;\n  private readonly duration: number;\n  private readonly windowSize: number;\n\n  private windows: IWindow[] = [];\n  private currentWindow = 0;\n  private currentFailures = 0;\n  private currentSuccesses = 0;\n\n  /**\n   * SamplingBreaker breaks if more than `threshold` percentage of calls over the\n   * last `samplingDuration`, so long as there's at least `minimumRps` (to avoid\n   * closing unnecessarily under low RPS).\n   */\n  constructor({ threshold, duration: samplingDuration, minimumRps }: ISamplingBreakerOptions) {\n    if (threshold <= 0 || threshold >= 1) {\n      throw new RangeError(`SamplingBreaker threshold should be between (0, 1), got ${threshold}`);\n    }\n\n    this.threshold = threshold;\n\n    // at least 5 windows, max 1 second each:\n    const windowCount = Math.max(5, Math.ceil(samplingDuration / 1000));\n    for (let i = 0; i < windowCount; i++) {\n      this.windows.push({ startedAt: 0, failures: 0, successes: 0 });\n    }\n\n    this.windowSize = Math.round(samplingDuration / windowCount);\n    this.duration = this.windowSize * windowCount;\n\n    if (minimumRps) {\n      this.minimumRpms = minimumRps / 1000;\n    } else {\n      // for our rps guess, set it so at least 5 failures per second\n      // are needed to open the circuit\n      this.minimumRpms = 5 / (threshold * 1000);\n    }\n  }\n\n  /**\n   * @inheritdoc\n   */\n  public success(state: CircuitState) {\n    if (state === CircuitState.HalfOpen) {\n      this.resetWindows();\n    }\n\n    this.push(true);\n  }\n\n  /**\n   * @inheritdoc\n   */\n  public failure(state: CircuitState) {\n    this.push(false);\n\n    if (state !== CircuitState.Closed) {\n      return true;\n    }\n\n    const total = this.currentSuccesses + this.currentFailures;\n\n    // If we don't have enough rps, then the circuit is open.\n    // 1. `total / samplingDuration` gets rps\n    // 2. We want `rpms < minimumRpms`\n    // 3. Simplifies to `total < samplingDuration * minimumRps`\n    if (total < this.duration * this.minimumRpms) {\n      return false;\n    }\n\n    // If we're above threshold, open the circuit\n    // 1. `failures / total > threshold`\n    // 2. `failures > threshold * total`\n    if (this.currentFailures > this.threshold * total) {\n      return true;\n    }\n\n    return false;\n  }\n\n  private resetWindows() {\n    this.currentFailures = 0;\n    this.currentSuccesses = 0;\n    for (const window of this.windows) {\n      window.failures = 0;\n      window.successes = 0;\n      window.startedAt = 0;\n    }\n  }\n\n  private rotateWindow(now: number) {\n    const next = (this.currentWindow + 1) % this.windows.length;\n    this.currentFailures -= this.windows[next].failures;\n    this.currentSuccesses -= this.windows[next].successes;\n    const window = (this.windows[next] = { failures: 0, successes: 0, startedAt: now });\n    this.currentWindow = next;\n\n    return window;\n  }\n\n  private push(success: boolean) {\n    const now = Date.now();\n\n    // Get the current time period window, advance if necessary\n    let window = this.windows[this.currentWindow];\n    if (now - window.startedAt >= this.windowSize) {\n      window = this.rotateWindow(now);\n    }\n\n    // Increment current counts\n    if (success) {\n      window.successes++;\n      this.currentSuccesses++;\n    } else {\n      window.failures++;\n      this.currentFailures++;\n    }\n  }\n}\n"]}